/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Friendly color palette */
  --primary-color: #4CAF50;      /* Friendly green */
  --secondary-color: #2196F3;    /* Calm blue */
  --accent-color: #FF9800;       /* Warm orange */
  --text-primary: #2E3440;       /* Dark gray */
  --text-secondary: #5E81AC;     /* Muted blue */
  --background: #FAFAFA;         /* Light gray */
  --surface: #FFFFFF;            /* Pure white */
  --border: #E0E0E0;             /* Light border */
  --shadow: rgba(0, 0, 0, 0.1);  /* Subtle shadow */
}

body {
  margin: 0;
  background-color: var(--background);
  color: var(--text-primary);
  min-height: 100vh;
  font-size: 16px;
}

#app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Landing View Styles */
.landing-view {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #E8F5E8 0%, #F0F8FF 100%);
}

.landing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
  max-width: 600px;
  text-align: center;
}

.landing-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

/* Test View Styles */
.test-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F8FF 0%, #E8F5E8 100%);
  position: relative;
}

.back-button {
  position: absolute;
  top: 2rem;
  left: 2rem;
  background-color: var(--surface);
  color: var(--text-secondary);
  border: 2px solid var(--border);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  z-index: 10;
}

.back-button:hover {
  background-color: var(--secondary-color);
  color: white;
  border-color: var(--secondary-color);
}

.test-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 4rem 2rem 2rem;
}

.test-passage {
  font-size: 1.5rem;
  line-height: 1.8;
  color: var(--text-primary);
  max-width: 700px;
  text-align: center;
  background-color: var(--surface);
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px var(--shadow);
  border: 1px solid var(--border);
}

.mic-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 2rem;
}

/* Mic Button Styles */
.mic-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color) 0%, #66BB6A 100%);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
  position: relative;
  overflow: hidden;
}

.mic-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.mic-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.test-mic {
  width: 100px;
  height: 100px;
}

.mic-button svg {
  width: 32px;
  height: 32px;
}

.test-mic svg {
  width: 40px;
  height: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing-title {
    font-size: 2rem;
  }

  .test-passage {
    font-size: 1.25rem;
    padding: 2rem;
  }

  .back-button {
    top: 1rem;
    left: 1rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .test-content {
    padding: 3rem 1rem 1rem;
  }

  .mic-container {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .landing-title {
    font-size: 1.75rem;
  }

  .test-passage {
    font-size: 1.1rem;
    padding: 1.5rem;
  }

  .landing-content {
    gap: 2rem;
  }
}
