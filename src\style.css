/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Friendly color palette */
  --primary-color: #4CAF50;      /* Friendly green */
  --secondary-color: #2196F3;    /* Calm blue */
  --accent-color: #FF9800;       /* Warm orange */
  --text-primary: #2C2C2C;       /* Soft black, easy on eyes */
  --text-secondary: #5E81AC;     /* Muted blue */
  --background: #F0F0F0;         /* Slightly darker gray-white */
  --surface: #FFFFFF;            /* Pure white */
  --border: #E0E0E0;             /* Light border */
  --shadow: rgba(0, 0, 0, 0.1);  /* Subtle shadow */
}

body {
  margin: 0;
  background-color: var(--background);
  color: var(--text-primary);
  min-height: 100vh;
  font-size: 16px;
}

#app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Landing View Styles */
.landing-view {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  background-color: var(--background);
}

.landing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
  max-width: 600px;
  text-align: center;
}

.landing-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

/* Test View Styles */
.test-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background);
  position: relative;
}

.test-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 4rem 2rem 1rem;
}

.test-passage {
  font-size: 2.5rem;
  line-height: 1.6;
  color: var(--text-primary);
  max-width: 1000px;
  text-align: center;
  /* Removed background, padding, border-radius, box-shadow, and border for floating effect */
}

.mic-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 2rem 4rem;
}

/* Mic Button Styles */
.mic-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #6B7280;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(107, 114, 128, 0.3);
  position: relative;
  overflow: hidden;
}

.mic-button:hover {
  transform: translateY(-2px);
  background: #4B5563;
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
}

.mic-button:active {
  transform: translateY(0);
  background: #374151;
  box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

.test-mic {
  width: 100px;
  height: 100px;
}

.mic-button svg {
  width: 32px;
  height: 32px;
}

.test-mic svg {
  width: 40px;
  height: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing-title {
    font-size: 2rem;
  }

  .test-passage {
    font-size: 2rem;
    max-width: 90%;
  }

  .test-content {
    padding: 3rem 1rem 1rem;
  }

  .mic-container {
    padding: 1.5rem 1rem 3rem;
  }
}

@media (max-width: 480px) {
  .landing-title {
    font-size: 1.75rem;
  }

  .test-passage {
    font-size: 1.75rem;
    max-width: 95%;
  }

  .landing-content {
    gap: 2rem;
  }

  .mic-container {
    padding: 1rem 1rem 2.5rem;
  }
}
