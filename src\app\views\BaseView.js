export class BaseView {
  constructor(container) {
    this.container = container
    this.element = null
  }

  render() {
    throw new Error('render() method must be implemented by subclass')
  }

  destroy() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element)
    }
    this.element = null
  }

  createElement(tag, className = '', innerHTML = '') {
    const element = document.createElement(tag)
    if (className) element.className = className
    if (innerHTML) element.innerHTML = innerHTML
    return element
  }

  show() {
    if (this.element) {
      this.element.style.display = 'block'
    }
  }

  hide() {
    if (this.element) {
      this.element.style.display = 'none'
    }
  }
}
