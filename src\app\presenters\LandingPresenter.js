import { BasePresenter } from './BasePresenter.js'
import { LandingView } from '../views/LandingView.js'
import { LandingModel } from '../models/LandingModel.js'

export class LandingPresenter extends BasePresenter {
  constructor(container, router) {
    super(container, router)
    this.model = new LandingModel()
    this.view = new LandingView(container)
  }

  init() {
    // Set up model observer
    this.model.addObserver(this)
    
    // Set up view event handlers
    this.view.setMicClickHandler(() => this.handleMicClick())
    
    // Render the view
    this.view.render()
  }

  handleMicClick() {
    // Navigate to test page when mic is clicked
    this.router.navigate('/test')
  }

  update(data) {
    // Handle model updates if needed
    console.log('Landing model updated:', data)
  }

  destroy() {
    if (this.model) {
      this.model.removeObserver(this)
    }
    super.destroy()
  }
}
