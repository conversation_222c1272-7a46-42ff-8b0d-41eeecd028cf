export class BasePresenter {
  constructor(container, router) {
    this.container = container
    this.router = router
    this.view = null
    this.model = null
  }

  init() {
    throw new Error('init() method must be implemented by subclass')
  }

  destroy() {
    if (this.view && this.view.destroy) {
      this.view.destroy()
    }
    this.view = null
    this.model = null
  }

  update(data) {
    // Default implementation for observer pattern
    // Override in subclasses as needed
  }
}
