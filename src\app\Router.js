export class Router {
  constructor() {
    this.routes = new Map()
    this.defaultRoute = '/'
  }

  addRoute(path, handler) {
    this.routes.set(path, handler)
  }

  setDefaultRoute(path) {
    this.defaultRoute = path
  }

  init() {
    // Handle initial load
    this.handleRoute()
    
    // Handle browser back/forward
    window.addEventListener('popstate', () => this.handleRoute())
  }

  navigate(path) {
    window.history.pushState({}, '', path)
    this.handleRoute()
  }

  handleRoute() {
    const path = window.location.pathname
    const handler = this.routes.get(path)
    
    if (handler) {
      handler()
    } else {
      // Navigate to default route if route not found
      this.navigate(this.defaultRoute)
    }
  }
}
