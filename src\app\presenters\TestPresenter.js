import { BasePresenter } from './BasePresenter.js'
import { TestView } from '../views/TestView.js'
import { TestModel } from '../models/TestModel.js'

export class TestPresenter extends BasePresenter {
  constructor(container, router) {
    super(container, router)
    this.model = new TestModel()
    this.view = new TestView(container)
  }

  init() {
    // Set up model observer
    this.model.addObserver(this)
    
    // Set up view event handlers
    this.view.setMicClickHandler(() => this.handleMicClick())
    this.view.setBackClickHandler(() => this.handleBackClick())
    
    // Render the view
    this.view.render()
  }

  handleMicClick() {
    if (this.model.isRecording()) {
      this.stopRecording()
    } else {
      this.startRecording()
    }
  }

  handleBackClick() {
    // Navigate back to landing page
    this.router.navigate('/')
  }

  startRecording() {
    this.model.startRecording()
    console.log('Recording started...')
    // Here you would implement actual recording logic
  }

  stopRecording() {
    this.model.stopRecording()
    console.log('Recording stopped...')
    // Here you would implement actual recording stop logic
  }

  update(data) {
    // Handle model updates
    console.log('Test model updated:', data)
    
    if (data.key === 'isRecording') {
      // Update UI based on recording state
      // You could add visual feedback here
    }
  }

  destroy() {
    if (this.model) {
      this.model.removeObserver(this)
    }
    super.destroy()
  }
}
